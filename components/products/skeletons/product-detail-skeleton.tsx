import { Skeleton } from '@/components/ui/skeleton';
import { cn } from '@/lib/utils';

export interface ProductDetailSkeletonProps {
  className?: string;
}

export function ProductDetailSkeleton({ className }: ProductDetailSkeletonProps) {
  return (
    <div className={cn('container mx-auto px-4 py-8', className)}>
      {/* Breadcrumb skeleton */}
      <div className="mb-8 flex items-center space-x-2">
        <Skeleton className="h-4 w-12" />
        <span className="text-muted-foreground">/</span>
        <Skeleton className="h-4 w-16" />
        <span className="text-muted-foreground">/</span>
        <Skeleton className="h-4 w-20" />
        <span className="text-muted-foreground">/</span>
        <Skeleton className="h-4 w-24" />
      </div>

      {/* Product details grid */}
      <div className="mb-16 grid grid-cols-1 gap-8 lg:grid-cols-2 lg:gap-12">
        {/* Product images skeleton */}
        <div className="space-y-4">
          {/* Main image */}
          <Skeleton className="aspect-square w-full rounded-lg" />

          {/* Thumbnail images */}
          <div className="flex space-x-2">
            {Array.from({ length: 4 }).map((_, index) => (
              <Skeleton key={index} className="aspect-square w-16 rounded-md" />
            ))}
          </div>
        </div>

        {/* Product information skeleton */}
        <div className="space-y-6">
          {/* Product title */}
          <div className="space-y-2">
            <Skeleton className="h-8 w-3/4" />
            <Skeleton className="h-4 w-1/2" />
          </div>

          {/* Price */}
          <div className="space-y-2">
            <Skeleton className="h-6 w-24" />
            <Skeleton className="h-4 w-32" />
          </div>

          {/* Description */}
          <div className="space-y-2">
            <Skeleton className="h-4 w-full" />
            <Skeleton className="h-4 w-5/6" />
            <Skeleton className="h-4 w-4/5" />
          </div>

          {/* Variants */}
          <div className="space-y-4">
            {/* Size variant */}
            <div className="space-y-2">
              <Skeleton className="h-4 w-12" />
              <div className="flex space-x-2">
                {Array.from({ length: 4 }).map((_, index) => (
                  <Skeleton key={index} className="h-10 w-12 rounded-md" />
                ))}
              </div>
            </div>

            {/* Color variant */}
            <div className="space-y-2">
              <Skeleton className="h-4 w-12" />
              <div className="flex space-x-2">
                {Array.from({ length: 3 }).map((_, index) => (
                  <Skeleton key={index} className="h-8 w-8 rounded-full" />
                ))}
              </div>
            </div>
          </div>

          {/* Quantity and add to cart */}
          <div className="space-y-4">
            <div className="flex items-center space-x-4">
              <Skeleton className="h-10 w-24" />
              <Skeleton className="h-10 flex-1" />
            </div>
            <Skeleton className="h-10 w-full" />
          </div>
        </div>
      </div>

      {/* Reviews section skeleton */}
      <div className="mb-16 space-y-6">
        <Skeleton className="h-6 w-32" />
        <div className="space-y-4">
          {Array.from({ length: 3 }).map((_, index) => (
            <div key={index} className="space-y-3 rounded-lg border p-4">
              <div className="flex items-center space-x-3">
                <Skeleton className="h-8 w-8 rounded-full" />
                <div className="space-y-1">
                  <Skeleton className="h-4 w-24" />
                  <Skeleton className="h-3 w-16" />
                </div>
              </div>
              <Skeleton className="h-4 w-full" />
              <Skeleton className="h-4 w-3/4" />
            </div>
          ))}
        </div>
      </div>

      {/* Related products skeleton */}
      <div className="space-y-6">
        <Skeleton className="h-6 w-40" />
        <div className="grid grid-cols-2 gap-4 md:grid-cols-4">
          {Array.from({ length: 4 }).map((_, index) => (
            <div key={index} className="space-y-3">
              <Skeleton className="aspect-square w-full rounded-lg" />
              <Skeleton className="h-4 w-3/4" />
              <Skeleton className="h-4 w-1/2" />
            </div>
          ))}
        </div>
      </div>
    </div>
  );
}
