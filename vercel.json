{"version": 2, "name": "vntg", "alias": ["vntg.vercel.app"], "regions": ["iad1"], "framework": "nextjs", "buildCommand": "npm run build", "outputDirectory": ".next", "installCommand": "npm ci", "devCommand": "npm run dev", "env": {"NODE_ENV": "production"}, "build": {"env": {"SENTRY_ORG": "@sentry-org", "SENTRY_PROJECT": "@sentry-project", "SENTRY_AUTH_TOKEN": "@sentry-auth-token"}}, "functions": {"app/api/**/*.ts": {"maxDuration": 30}, "app/api/webhooks/**/*.ts": {"maxDuration": 60}}, "headers": [{"source": "/(.*)", "headers": [{"key": "X-Content-Type-Options", "value": "nosniff"}, {"key": "X-Frame-Options", "value": "DENY"}, {"key": "X-XSS-Protection", "value": "1; mode=block"}, {"key": "Referrer-Policy", "value": "strict-origin-when-cross-origin"}, {"key": "Permissions-Policy", "value": "camera=(), microphone=(), geolocation=()"}]}, {"source": "/api/(.*)", "headers": [{"key": "Access-Control-Allow-Origin", "value": "https://yourdomain.com"}, {"key": "Access-Control-Allow-Methods", "value": "GET, POST, PUT, DELETE, OPTIONS"}, {"key": "Access-Control-Allow-Headers", "value": "Content-Type, Authorization"}]}], "redirects": [{"source": "/admin", "destination": "/admin/dashboard", "permanent": false}, {"source": "/account", "destination": "/account/dashboard", "permanent": false}], "rewrites": [{"source": "/sitemap.xml", "destination": "/api/sitemap"}, {"source": "/robots.txt", "destination": "/api/robots"}], "crons": [{"path": "/api/cron/cleanup-abandoned-carts", "schedule": "0 2 * * *"}, {"path": "/api/cron/update-inventory", "schedule": "*/15 * * * *"}, {"path": "/api/cron/process-analytics", "schedule": "0 1 * * *"}]}