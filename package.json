{"name": "vntg", "version": "1.0.0", "description": "E-Commerce Platform", "private": true, "scripts": {"dev": "next dev", "build": "next build", "start": "next start", "lint": "next lint", "lint:fix": "next lint --fix", "type-check": "tsc --noEmit", "format": "prettier --write .", "format:check": "prettier --check .", "test": "jest", "test:watch": "jest --watch", "test:coverage": "jest --coverage", "test:ci": "jest --ci --coverage --watchAll=false", "test:e2e": "playwright test", "test:e2e:ui": "playwright test --ui", "test:e2e:headed": "playwright test --headed", "test:e2e:debug": "playwright test --debug", "test:all": "npm run type-check && npm run lint && npm run test:ci && npm run test:e2e", "db:migrate": "supabase db push", "db:migrate:prod": "node scripts/migrate-database.js", "db:migrate:status": "node scripts/migrate-database.js --status", "db:reset": "supabase db reset", "db:seed": "node scripts/seed.js", "db:backup": "node scripts/backup-restore.js backup", "db:restore": "node scripts/backup-restore.js restore", "db:list-backups": "node scripts/backup-restore.js list-backups", "deploy:production": "./scripts/deploy-production.sh", "deploy:rollback": "./scripts/rollback-deployment.sh", "analyze": "ANALYZE=true npm run build"}, "dependencies": {"@hookform/resolvers": "^3.3.4", "@radix-ui/react-avatar": "^1.0.4", "@radix-ui/react-checkbox": "^1.3.2", "@radix-ui/react-dialog": "^1.1.14", "@radix-ui/react-dropdown-menu": "^2.0.6", "@radix-ui/react-label": "^2.0.2", "@radix-ui/react-select": "^2.2.5", "@radix-ui/react-separator": "^1.1.7", "@radix-ui/react-slider": "^1.3.5", "@radix-ui/react-slot": "^1.2.3", "@radix-ui/react-toast": "^1.2.14", "@sentry/nextjs": "^9.30.0", "@stripe/react-stripe-js": "^3.7.0", "@stripe/stripe-js": "^2.3.0", "@supabase/auth-helpers-nextjs": "^0.8.7", "@supabase/supabase-js": "^2.39.3", "class-variance-authority": "^0.7.0", "clsx": "^2.1.0", "gsap": "^3.12.5", "lucide-react": "^0.309.0", "next": "14.1.0", "next-themes": "^0.2.1", "react": "^18.2.0", "react-dom": "^18.2.0", "react-hook-form": "^7.49.3", "recharts": "^2.15.4", "resend": "^4.6.0", "stripe": "^14.12.0", "tailwind-merge": "^2.2.0", "tailwindcss-animate": "^1.0.7", "zod": "^3.22.4"}, "devDependencies": {"@playwright/test": "^1.53.1", "@testing-library/jest-dom": "^6.6.3", "@testing-library/react": "^16.3.0", "@testing-library/user-event": "^14.6.1", "@types/jest": "^30.0.0", "@types/node": "^20.11.5", "@types/react": "^18.2.48", "@types/react-dom": "^18.2.18", "autoprefixer": "^10.4.16", "eslint": "^8.56.0", "eslint-config-next": "14.1.0", "jest": "^30.0.2", "jest-environment-jsdom": "^30.0.2", "msw": "^2.10.2", "node-mocks-http": "^1.17.2", "postcss": "^8.4.33", "prettier": "^3.2.4", "prettier-plugin-tailwindcss": "^0.5.11", "tailwindcss": "^3.4.1", "typescript": "^5.3.3"}}