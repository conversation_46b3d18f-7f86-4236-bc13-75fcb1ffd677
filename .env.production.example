# =============================================================================
# PRODUCTION ENVIRONMENT VARIABLES
# =============================================================================
# Copy this file to .env.production and fill in your actual values
# Never commit actual production values to version control

# =============================================================================
# APP CONFIGURATION
# =============================================================================
NODE_ENV=production
NEXT_PUBLIC_APP_URL=https://yourdomain.com
NEXT_PUBLIC_APP_NAME="VNTG"

# =============================================================================
# SUPABASE CONFIGURATION
# =============================================================================
# Get these from your Supabase project dashboard
NEXT_PUBLIC_SUPABASE_URL=https://your-project-id.supabase.co
NEXT_PUBLIC_SUPABASE_ANON_KEY=your_supabase_anon_key_here
SUPABASE_SERVICE_ROLE_KEY=your_supabase_service_role_key_here

# Database connection (if using direct connection)
DATABASE_URL=postgresql://postgres:<EMAIL>:5432/postgres

# =============================================================================
# STRIPE CONFIGURATION
# =============================================================================
# Get these from your Stripe dashboard (use LIVE keys for production)
NEXT_PUBLIC_STRIPE_PUBLISHABLE_KEY=pk_live_your_stripe_publishable_key_here
STRIPE_SECRET_KEY=sk_live_your_stripe_secret_key_here
STRIPE_WEBHOOK_SECRET=whsec_your_stripe_webhook_secret_here

# =============================================================================
# EMAIL CONFIGURATION
# =============================================================================
# Choose one email provider

# Option 1: Resend (recommended)
RESEND_API_KEY=re_your_resend_api_key_here
RESEND_FROM_EMAIL=<EMAIL>

# Option 2: SendGrid
# SENDGRID_API_KEY=SG.your_sendgrid_api_key_here
# SENDGRID_FROM_EMAIL=<EMAIL>

# Option 3: Nodemailer SMTP
# SMTP_HOST=smtp.yourdomain.com
# SMTP_PORT=587
# SMTP_USER=your_smtp_username
# SMTP_PASS=your_smtp_password
# SMTP_FROM=<EMAIL>

# =============================================================================
# MONITORING & ANALYTICS
# =============================================================================
# Sentry for error tracking
SENTRY_DSN=https://<EMAIL>/project_id
SENTRY_ORG=your_sentry_org
SENTRY_PROJECT=your_sentry_project
SENTRY_AUTH_TOKEN=your_sentry_auth_token

# Google Analytics
NEXT_PUBLIC_GOOGLE_ANALYTICS_ID=G-XXXXXXXXXX

# Vercel Analytics (automatically enabled on Vercel)
NEXT_PUBLIC_VERCEL_ANALYTICS=true

# =============================================================================
# SECURITY
# =============================================================================
# JWT secrets (generate strong random strings)
JWT_SECRET=your_super_secure_jwt_secret_here
NEXTAUTH_SECRET=your_nextauth_secret_here
NEXTAUTH_URL=https://yourdomain.com

# Rate limiting
UPSTASH_REDIS_REST_URL=https://your-redis-url.upstash.io
UPSTASH_REDIS_REST_TOKEN=your_redis_token

# =============================================================================
# STORAGE & CDN
# =============================================================================
# Supabase Storage (automatically configured with Supabase)
NEXT_PUBLIC_SUPABASE_STORAGE_URL=https://your-project-id.supabase.co/storage/v1

# Optional: External CDN
# CDN_URL=https://cdn.yourdomain.com

# =============================================================================
# SEARCH & INDEXING
# =============================================================================
# Optional: Algolia for advanced search
# NEXT_PUBLIC_ALGOLIA_APP_ID=your_algolia_app_id
# ALGOLIA_ADMIN_API_KEY=your_algolia_admin_key
# NEXT_PUBLIC_ALGOLIA_SEARCH_API_KEY=your_algolia_search_key

# =============================================================================
# SHIPPING & LOGISTICS
# =============================================================================
# Optional: Shipping providers
# SHIPPO_API_KEY=your_shippo_api_key
# FEDEX_API_KEY=your_fedex_api_key
# UPS_API_KEY=your_ups_api_key

# =============================================================================
# SOCIAL AUTH (if using)
# =============================================================================
# Google OAuth
# GOOGLE_CLIENT_ID=your_google_client_id
# GOOGLE_CLIENT_SECRET=your_google_client_secret

# GitHub OAuth
# GITHUB_CLIENT_ID=your_github_client_id
# GITHUB_CLIENT_SECRET=your_github_client_secret

# Facebook OAuth
# FACEBOOK_CLIENT_ID=your_facebook_client_id
# FACEBOOK_CLIENT_SECRET=your_facebook_client_secret

# =============================================================================
# FEATURE FLAGS
# =============================================================================
# Enable/disable features in production
NEXT_PUBLIC_ENABLE_ANALYTICS=true
NEXT_PUBLIC_ENABLE_CHAT_SUPPORT=true
NEXT_PUBLIC_ENABLE_WISHLIST=true
NEXT_PUBLIC_ENABLE_REVIEWS=true
NEXT_PUBLIC_ENABLE_LOYALTY_PROGRAM=false

# =============================================================================
# PERFORMANCE & CACHING
# =============================================================================
# Redis for caching (optional)
REDIS_URL=redis://localhost:6379

# Image optimization
NEXT_PUBLIC_ENABLE_IMAGE_OPTIMIZATION=true
NEXT_PUBLIC_IMAGE_DOMAINS=yourdomain.com,your-project-id.supabase.co

# =============================================================================
# DEVELOPMENT & DEBUGGING
# =============================================================================
# Set to 'true' only for debugging production issues
DEBUG=false
VERBOSE_LOGGING=false

# =============================================================================
# BACKUP & RECOVERY
# =============================================================================
# Database backup configuration
# BACKUP_SCHEDULE=0 2 * * *  # Daily at 2 AM
# BACKUP_RETENTION_DAYS=30
# BACKUP_STORAGE_URL=s3://your-backup-bucket

# =============================================================================
# COMPLIANCE & LEGAL
# =============================================================================
# GDPR compliance
NEXT_PUBLIC_ENABLE_COOKIE_CONSENT=true
NEXT_PUBLIC_PRIVACY_POLICY_URL=https://yourdomain.com/privacy
NEXT_PUBLIC_TERMS_OF_SERVICE_URL=https://yourdomain.com/terms

# =============================================================================
# NOTES
# =============================================================================
# 1. Replace all placeholder values with your actual production values
# 2. Keep this file secure and never commit it to version control
# 3. Use environment-specific values for each deployment environment
# 4. Regularly rotate secrets and API keys
# 5. Monitor usage and costs for third-party services
